import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;

class QuestionIndicators extends StatelessWidget {
  final entities.Question question;
  final bool isMandatory;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final bool hasPhotoUrl;

  const QuestionIndicators({
    super.key,
    required this.question,
    required this.isMandatory,
    required this.showCameraIcon,
    required this.isCameraMandatory,
    required this.hasPhotoUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Mandatory indicator (exclamation mark)
        if (isMandatory)
          Positioned(
            top: 16,
            right: 12,
            child: Container(
              width: 16,
              height: 16,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.priority_high,
                color: Colors.white,
                size: 10,
              ),
            ),
          ),
        // Photo URL indicator (blue image icon)
        if (hasPhotoUrl)
          Positioned(
            top: isMandatory ? 32 : 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                // Open image in viewer using WebBrowserRoute
                if (question.photoUrl != null) {
                  AutoRouter.of(context)
                      .push(WebBrowserRoute(url: question.photoUrl!));
                }
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.image,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        // Camera icon

        if (showCameraIcon)
          Positioned(
            top: _calculateCameraIconTop(isMandatory, hasPhotoUrl),
            right: 14,
            child: GestureDetector(
              onTap: () {
                // TODO: Implement camera functionality
              },
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: isCameraMandatory ? Colors.amber : Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Calculate the top position for camera icon based on other indicators
  double _calculateCameraIconTop(bool isMandatory, bool hasPhotoUrl) {
    if (isMandatory && hasPhotoUrl) {
      return 56; // Below both mandatory and photo indicators
    } else if (isMandatory || hasPhotoUrl) {
      return 34; // Below one indicator
    } else {
      return 8; // No other indicators
    }
  }
}
