import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_widget.dart'
    show PhotoUploadWidget;

class TextFieldWidget extends StatefulWidget {
  final Measurement measurement;
  final String value;
  final Function(String) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;

  const TextFieldWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
  });

  @override
  State<TextFieldWidget> createState() => _TextFieldWidgetState();
}

class _TextFieldWidgetState extends State<TextFieldWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
  }

  @override
  void didUpdateWidget(TextFieldWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.measurement.measurementDescription ?? 'Enter Text',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (widget.isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
            ],
          ),
          const Gap(16),
          TextFormField(
            controller: _controller,
            onChanged: widget.onChanged,
            decoration: InputDecoration(
              hintText: 'Enter your text here...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.blackTint2),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.blackTint2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                // borderSide:
                //     const BorderSide(color: AppColors.primaryBlue, width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 16.0,
              ),
            ),
            style: textTheme.bodyMedium?.copyWith(
              color: AppColors.black,
              fontSize: 15,
            ),
            validator: (value) {
              if (widget.measurement.required == true &&
                  (value == null || value.isEmpty)) {
                return 'This field is required';
              }
              return null;
            },
          ),
          // Camera section
          if (widget.showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              onCameraPressed: () {
                // Handle camera button press
                print('Camera button pressed');
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Camera button pressed'),
                    duration: Duration(seconds: 1),
                  ),
                );
              },
            ),
            // Row(
            //   children: [
            //     // HI-RES Button
            //     Container(
            //       padding: const EdgeInsets.symmetric(
            //         horizontal: 12.0,
            //         vertical: 6.0,
            //       ),
            //       decoration: BoxDecoration(
            //         color: AppColors.lightGrey2,
            //         borderRadius: BorderRadius.circular(6),
            //         border: Border.all(
            //           color: AppColors.blackTint2,
            //           width: 1,
            //         ),
            //       ),
            //       child: Text(
            //         'HI-RES',
            //         style: textTheme.bodySmall?.copyWith(
            //           color: AppColors.blackTint1,
            //           fontSize: 11,
            //           fontWeight: FontWeight.w600,
            //         ),
            //       ),
            //     ),
            //     const Gap(12),
            //     // Camera Icon
            //     GestureDetector(
            //       onTap: widget.onCameraTap,
            //       child: Container(
            //         width: 40,
            //         height: 40,
            //         decoration: BoxDecoration(
            //           color: widget.isCameraMandatory
            //               ? Colors.amber
            //               : Colors.green,
            //           borderRadius: BorderRadius.circular(8),
            //         ),
            //         child: const Icon(
            //           Icons.camera_alt,
            //           color: Colors.white,
            //           size: 20,
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
          ],
          if (widget.measurement.required == true && widget.value.isEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'This field is required',
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
