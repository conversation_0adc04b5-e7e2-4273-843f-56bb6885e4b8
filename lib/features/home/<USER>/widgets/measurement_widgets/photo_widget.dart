import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class PhotoWidget extends StatelessWidget {
  final Measurement measurement;
  final List<String> photoUrls;
  final Function(List<String>) onPhotosChanged;
  final VoidCallback? onCameraTap;
  final bool isMandatory;
  final String? subtitle;

  const PhotoWidget({
    super.key,
    required this.measurement,
    required this.photoUrls,
    required this.onPhotosChanged,
    this.onCameraTap,
    this.isMandatory = false,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            measurement.measurementDescription ?? 'Add photos',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
          if (subtitle != null) ...[
            const Gap(4),
            Text(
              subtitle!,
              style: textTheme.bodySmall?.copyWith(
                color: AppColors.blackTint1,
                fontSize: 13,
              ),
            ),
          ],
          const Gap(16),
          Row(
            children: [
              // HI-RES Button
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 6.0,
                ),
                decoration: BoxDecoration(
                  color: AppColors.lightGrey2,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: AppColors.blackTint2,
                    width: 1,
                  ),
                ),
                child: Text(
                  'HI-RES',
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.blackTint1,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Gap(12),
              // Camera Icon
              GestureDetector(
                onTap: onCameraTap,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isMandatory ? Colors.amber : Colors.green,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
          // Photo thumbnails if any exist
          if (photoUrls.isNotEmpty) ...[
            const Gap(16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  photoUrls.map((url) => _buildPhotoThumbnail(url)).toList(),
            ),
          ],
          // Required validation
          if (measurement.required == true && photoUrls.isEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                'This field is required',
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPhotoThumbnail(String url) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.blackTint2,
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(7),
        child: Image.network(
          url,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppColors.lightGrey2,
              child: const Icon(
                Icons.broken_image,
                color: AppColors.blackTint1,
                size: 24,
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              color: AppColors.lightGrey2,
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
