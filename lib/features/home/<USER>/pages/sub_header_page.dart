import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class SubHeaderPage extends StatelessWidget {
  final String title;
  final List<entities.QuestionPart> questionParts;
  final entities.Question question;

  const SubHeaderPage({
    super.key,
    required this.title,
    required this.questionParts,
    required this.question,
  });

  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements, then loop through measurement_validations to check if any "required" key is true
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            print('hi------11112');

            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                print('hi------1111');
                return true;
              }
            }
          }
        }
      }
    } else if (question.isMulti != true) {
      print('hi------111123');

      // Check if isSupplementaryQuestion is true and isOneOption is true
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // Check if isOneOption is true (any questionpart_id contains "-")
        if (question.questionParts != null) {
          for (final questionPart in question.questionParts!) {
            if (questionPart.questionpartId != null &&
                questionPart.questionpartId.toString().contains("-")) {
              return true;
            }
          }
        }
      }
    }
    print('hi------1111235');

    return false;
  }

  @override
  Widget build(BuildContext context) {
    print('hi-----${question.measurements}');
    final textTheme = Theme.of(context).textTheme;
    final isMandatory = _isQuestionMandatory(question);

    return Scaffold(
      backgroundColor: AppColors.lightGrey1, // Set background color to grey
      appBar: CustomAppBar(
        title: title,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            questionParts.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No items available for this section',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    itemCount: questionParts.length,
                    itemBuilder: (context, index) {
                      final part = questionParts[index];
                      return _buildItemCard(context, part, isMandatory);
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const Gap(8);
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }

  /// Build item card without progress bar and with grey background
  Widget _buildItemCard(
      BuildContext context, entities.QuestionPart part, bool isMandatory) {
    return Stack(
      children: [
        Card(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          elevation: 1,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: () {
              // Use the original question object instead of creating a new one
              context.router.push(QPMDRoute(question: question));
            },
            child: SizedBox(
              width: double.infinity,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      part.questionpartDescription ?? 'Unnamed Part',
                      style:
                          Theme.of(context).textTheme.montserratTitleExtraSmall,
                    ),
                    // No progress bar as per requirements
                  ],
                ),
              ),
            ),
          ),
        ),
        // Mandatory indicator (exclamation mark) if question is mandatory
        // if (isMandatory)
        //   Positioned(
        //     top: 0,
        //     right: 12,
        //     bottom: 0,
        //     child: Container(
        //       width: 20,
        //       height: 20,
        //       decoration: const BoxDecoration(
        //         color: Colors.red,
        //         shape: BoxShape.circle,
        //       ),
        //       child: const Icon(
        //         Icons.priority_high,
        //         color: Colors.white,
        //         size: 14,
        //       ),
        //     ),
        //   ),
      ],
    );
  }
}
