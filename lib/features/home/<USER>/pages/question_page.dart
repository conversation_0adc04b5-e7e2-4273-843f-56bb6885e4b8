import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/question_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/comment_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_questions_state.dart';

@RoutePage()
class QuestionPage extends StatefulWidget {
  final entities.Form form;

  const QuestionPage({
    super.key,
    required this.form,
  });

  @override
  State<QuestionPage> createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  // State for comment fields
  final Map<num, String?> _selectedCommentTypes = {};
  final Map<num, TextEditingController> _commentControllers = {};
  // Get questions from the form
  List<entities.Question>? get questionItems => widget.form.questions;

  @override
  void initState() {
    super.initState();
    if (widget.form.questions != null) {
      for (final question in widget.form.questions!) {
        if (question.isComment == true && question.questionId != null) {
          _commentControllers[question.questionId!] = TextEditingController();
          // Initialize with null or a default first item if applicable
          _selectedCommentTypes[question.questionId!] = null;
        }
      }
    }
  }

  @override
  void dispose() {
    _commentControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements, then loop through measurement_validations to check if any "required" key is true
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                return true;
              }
            }
          }
        }
      }
    } else if (question.isMulti != true) {
      // Check if isSupplementaryQuestion is true and isOneOption is true
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // Check if isOneOption is true (any questionpart_id contains "-")
        if (question.questionParts != null) {
          for (final questionPart in question.questionParts!) {
            if (questionPart.questionpartId != null &&
                questionPart.questionpartId.toString().contains("-")) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  /// Get camera icon info for a question based on photo_tags_two array
  Map<String, dynamic> _getCameraIconInfo(entities.Question question) {
    final result = {'show': false, 'isMandatory': false};

    if (question.photoTagsTwo == null || question.photoTagsTwo!.isEmpty) {
      return result;
    }

    result['show'] = true;

    // Check if any item has is_mandatory set to true
    for (final photoTag in question.photoTagsTwo!) {
      if (photoTag.isMandatory == true) {
        result['isMandatory'] = true;
        break;
      }
    }

    return result;
  }

  /// Check if question has a photo URL
  bool _hasPhotoUrl(entities.Question question) {
    return question.photoUrl != null && question.photoUrl!.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.form.formName ?? 'Questions',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            questionItems == null || questionItems!.isEmpty
                ? const EmptyQuestionsState()
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 8.0),
                    itemCount: questionItems!.length,
                    itemBuilder: (context, index) {
                      final question = questionItems![index];

                      if (question.isComment == true &&
                          question.questionId != null) {
                        final questionId = question.questionId!;
                        // Ensure controller and selected value exists, though initState should handle this
                        if (!_commentControllers.containsKey(questionId)) {
                          _commentControllers[questionId] =
                              TextEditingController();
                        }
                        if (!_selectedCommentTypes.containsKey(questionId)) {
                          _selectedCommentTypes[questionId] = null;
                        }

                        return CommentCard(
                          question: question,
                          selectedCommentType:
                              _selectedCommentTypes[questionId],
                          commentController: _commentControllers[questionId]!,
                          onCommentTypeChanged: (String? newValue) {
                            setState(() {
                              _selectedCommentTypes[questionId] = newValue;
                            });
                          },
                        );
                      } else {
                        // Calculate progress based on measurements if available
                        double progress = 0.0;
                        String progressText = '0 of 0';

                        if (question.measurements != null &&
                            question.measurements!.isNotEmpty) {
                          int totalMeasurements = question.measurements!.length;
                          // This could be enhanced later with actual progress tracking
                          progressText = '0 of $totalMeasurements';
                        }

                        final isMandatory = _isQuestionMandatory(question);
                        final cameraInfo = _getCameraIconInfo(question);
                        final hasPhoto = _hasPhotoUrl(question);

                        return QuestionCard(
                          question: question,
                          progress: progress,
                          progressText: progressText,
                          isMandatory: isMandatory,
                          showCameraIcon: cameraInfo['show'] as bool,
                          isCameraMandatory: cameraInfo['isMandatory'] as bool,
                          hasPhotoUrl: hasPhoto,
                        );
                      }
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const Gap(8);
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
