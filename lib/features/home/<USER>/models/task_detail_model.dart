import 'package:realm/realm.dart';
part 'task_detail_model.realm.dart';

// Main Task Model, updated to match JSON structure
@RealmModel()
class _TaskDetailModel {
  @PrimaryKey()
  @MapTo('task_id')
  int id = 0;

  bool isSynced = false;

  @MapTo('project_id')
  int? projectId;

  @MapTo('schedule_id')
  int? scheduleId;

  @MapTo('sent_to_payroll')
  bool? sentToPayroll;

  @MapTo('show_km')
  bool? showKm;

  @MapTo('store_id')
  int? storeId;

  @MapTo('client')
  String? client;

  @MapTo('client_id')
  int? clientId;

  @MapTo('client_logo_url')
  String? clientLogoUrl;

  @MapTo('store_group')
  String? storeGroup;

  @MapTo('store_group_id')
  int? storeGroupId;

  @MapTo('store_name')
  String? storeName;

  @MapTo('store_email')
  String? storeEmail;

  @MapTo('minutes')
  int? minutes;

  @MapTo('budget')
  int? budget;

  @MapTo('originalbudget')
  int? originalbudget;

  @MapTo('comment')
  String? comment;

  @MapTo('claimable_kms')
  int? claimableKms;

  @MapTo('flight_duration')
  int? flightDuration;

  @MapTo('pages')
  int? pages;

  @MapTo('location')
  String? location;

  @MapTo('suburb')
  String? suburb;

  @MapTo('latitude')
  double? latitude;

  @MapTo('longitude')
  double? longitude;

  @MapTo('task_latitude')
  double? taskLatitude;

  @MapTo('task_longitude')
  double? taskLongitude;

  @MapTo('cycle')
  String? cycle;

  @MapTo('cycle_id')
  int? cycleId;

  @MapTo('can_delete')
  bool? canDelete;

  @MapTo('scheduled_time_stamp')
  DateTime? scheduledTimeStamp;

  @MapTo('submission_time_stamp')
  DateTime? submissionTimeStamp;

  @MapTo('expires')
  DateTime? expires;

  @MapTo('on_task')
  String? onTask;

  @MapTo('phone')
  String? phone;

  @MapTo('range_start')
  DateTime? rangeStart;

  @MapTo('range_end')
  DateTime? rangeEnd;

  @MapTo('re_opened')
  bool? reOpened;

  @MapTo('re_opened_reason')
  String? reOpenedReason;

  @MapTo('task_status')
  String? taskStatus;

  @MapTo('warehousejob_id')
  int? warehousejobId;

  @MapTo('connote_url')
  String? connoteUrl;

  @MapTo('pos_required')
  bool? posRequired;

  @MapTo('is_pos_mandatory')
  bool? isPosMandatory;

  @MapTo('pos_received')
  String? posReceived;

  @MapTo('photo_folder')
  late List<_PhotoFolderModel> photoFolder;

  @MapTo('signature_folder')
  late List<_SignatureFolderModel> signatureFolder;

  @MapTo('forms')
  late List<_FormModel> forms;

  @MapTo('pos_items')
  late List<_PosItemModel> posItems;

  @MapTo('documents')
  late List<_DocumentModel> documents;

  @MapTo('taskalerts')
  late List<_TaskalertModel> taskalerts;

  @MapTo('taskmembers')
  late List<_TaskmemberModel> taskmembers;

  @MapTo('modified_time_stamp_documents')
  DateTime? modifiedTimeStampDocuments;

  @MapTo('modified_time_stamp_forms')
  DateTime? modifiedTimeStampForms;

  @MapTo('modified_time_stamp_members')
  DateTime? modifiedTimeStampMembers;

  @MapTo('modified_time_stamp_task')
  DateTime? modifiedTimeStampTask;

  @MapTo('modified_time_stamp_photos')
  DateTime? modifiedTimeStampPhotos;

  @MapTo('modified_time_stamp_signatures')
  DateTime? modifiedTimeStampSignatures;

  @MapTo('modified_time_stamp_signaturetypes')
  DateTime? modifiedTimeStampSignaturetypes;

  @MapTo('Pos_Sent_To')
  String? posSentTo;

  @MapTo('Pos_Sent_To_Email')
  String? posSentToEmail;

  @MapTo('modified_time_stamp_phototypes')
  DateTime? modifiedTimeStampPhototypes;

  @MapTo('task_commencement_time_stamp')
  DateTime? taskCommencementTimeStamp;

  @MapTo('task_stopped_time_stamp')
  DateTime? taskStoppedTimeStamp;

  @MapTo('teamlead')
  int? teamlead;

  @MapTo('followup_tasks')
  late List<_FollowupTaskModel> followupTasks;

  @MapTo('stocktake')
  late List<_StocktakeModel> stocktake;

  @MapTo('task_note')
  String? taskNote;

  @MapTo('disallow_reschedule')
  bool? disallowReschedule;

  @MapTo('PhotoResPerc')
  int? photoResPerc;

  @MapTo('live_images_only')
  bool? liveImagesOnly;

  @MapTo('time_schedule')
  String? timeSchedule;

  @MapTo('ScheduleTypeID')
  int? scheduleTypeId;

  @MapTo('show_followup_icon_multi')
  bool? showFollowupIconMulti;

  @MapTo('followup_selected_multi')
  bool? followupSelectedMulti;

  @MapTo('region_id')
  int? regionId;

  @MapTo('is_open')
  bool? isOpen;

  @MapTo('task_count')
  int? taskCount;

  @MapTo('ct_forms_total_cnt')
  int? ctFormsTotalCnt;

  @MapTo('ct_forms_completed_cnt')
  int? ctFormsCompletedCnt;

  @MapTo('preftime')
  String? preftime;

  @MapTo('send_to')
  bool? sendTo;
}

// --- Photo and Signature Models ---

@RealmModel(ObjectType.embeddedObject)
abstract class _PhotoFolderModel {
  @MapTo('photos')
  late List<_PhotoModel> photos;

  @MapTo('attribute')
  bool? attribute;

  @MapTo('folder_id')
  int? folderId;

  @MapTo('folder_name')
  String? name;

  @MapTo('folder_picture_amount')
  int? folderPictureAmount;

  @MapTo('image_rec')
  bool? imageRec;

  @MapTo('modified_time_stamp_phototype')
  DateTime? modifiedTimeStampPhototype;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _PhotoModel {
  @MapTo('photo_id')
  int? photoId;

  @MapTo('photo_url')
  String? photoUrl;

  @MapTo('thumbnail_url')
  String? thumbnailUrl;

  @MapTo('caption')
  String? caption;

  @MapTo('modified_time_stamp_photo')
  DateTime? modifiedTimeStampPhoto;

  @MapTo('cannot_upload_mandatory')
  bool? cannotUploadMandatory;

  @MapTo('user_deleted_photo')
  bool? userDeletedPhoto;

  @MapTo('image_rec')
  bool? imageRec;

  @MapTo('form_id')
  int? formId;

  @MapTo('question_id')
  int? questionId;

  @MapTo('measurement_id')
  int? measurementId;

  @MapTo('questionpart_id')
  int? questionpartId;

  @MapTo('question_part_multi_id')
  String? questionPartMultiId;

  @MapTo('measurement_phototype_id')
  int? measurementPhototypeId;

  @MapTo('combine_type_id')
  int? combineTypeId;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _SignatureFolderModel {
  @MapTo('folder_name')
  String? name;

  @MapTo('signatures')
  late List<String> signatures;

  @MapTo('attribute')
  bool? attribute;

  @MapTo('folder_id')
  int? folderId;

  @MapTo('modified_time_stamp_signaturetype')
  DateTime? modifiedTimeStampSignaturetype;
}

// --- Form and Question Models (Highly expanded) ---

@RealmModel(ObjectType.embeddedObject)
abstract class _FormModel {
  @MapTo('form_id')
  int? formId;

  @MapTo('form_name')
  String? formName;

  String? formType; // Keep from original, though not in JSON

  @MapTo('question_answers')
  late List<_QuestionAnswerModel> answers;

  @MapTo('form_instance_id')
  int? formInstanceId;

  @MapTo('brief_url')
  String? briefUrl;

  @MapTo('questions')
  late List<_QuestionModel> questions;

  @MapTo('modified_time_stamp_form')
  DateTime? modifiedTimeStampForm;

  @MapTo('date_start')
  DateTime? dateStart;

  @MapTo('is_vision_form')
  bool? isVisionForm;

  @MapTo('vision_form_url')
  String? visionFormUrl;

  @MapTo('is_mandatory')
  bool? isMandatory;

  @MapTo('form_preview')
  bool? formPreview;

  @MapTo('form_type_id')
  int? formTypeId;

  @MapTo('form_completed')
  bool? formCompleted;

  @MapTo('form_allowForward')
  bool? formAllowForward;

  @MapTo('form_show_price')
  bool? formShowPrice;

  @MapTo('min_qty')
  int? minQty;

  @MapTo('show_questions')
  bool? showQuestions;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _QuestionModel {
  @MapTo('question_id')
  int? questionId;

  @MapTo('question_order_id')
  int? questionOrderId;

  @MapTo('question_description')
  String? questionDescription;

  @MapTo('is_comment')
  bool? isComment;

  @MapTo('is_comment_mandatory')
  bool? isCommentMandatory;

  @MapTo('show_questions')
  bool? showQuestions;

  @MapTo('has_signature')
  bool? hasSignature;

  @MapTo('is_signature_mandatory')
  bool? isSignatureMandatory;

  @MapTo('signature_url')
  String? signatureUrl;

  @MapTo('photo_url')
  String? photoUrl;

  @MapTo('question_brief')
  String? questionBrief;

  @MapTo('question_parts')
  late List<_QuestionPartModel> questionParts;

  @MapTo('measurements')
  late List<_MeasurementModel> measurements;

  @MapTo('question_conditions')
  late List<_QuestionConditionModel> questionConditions;

  @MapTo('comment_types')
  late List<_CommentTypeModel> commentTypes;

  @MapTo('modified_time_stamp_question')
  DateTime? modifiedTimeStampQuestion;

  @MapTo('targetByCycle')
  bool? targetByCycle;

  @MapTo('targetByGroup')
  bool? targetByGroup;

  @MapTo('targetByCompany')
  bool? targetByCompany;

  @MapTo('targetByRegion')
  bool? targetByRegion;

  @MapTo('targetByBudget')
  bool? targetByBudget;

  @MapTo('is_mll')
  bool? isMll;

  @MapTo('photo_tags_two')
  late List<_PhotoTagModel> photoTagsTwo;

  @MapTo('photo_tags_three')
  late List<_PhotoTagModel> photoTagsThree;

  @MapTo('is_multi')
  bool? isMulti;

  @MapTo('multi_measurement_id')
  int? multiMeasurementId;

  @MapTo('is_multi_one_answer')
  bool? isMultiOneAnswer;

  @MapTo('flip')
  bool? flip;

  @MapTo('question_type_id')
  int? questionTypeId;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _QuestionPartModel {
  @MapTo('projectid')
  int? projectId;

  @MapTo('questionpart_id')
  int? questionpartId;

  @MapTo('questionpart_description')
  String? questionpartDescription;

  @MapTo('price')
  String? price;

  @MapTo('modified_time_stamp_questionpart')
  DateTime? modifiedTimeStampQuestionpart;

  @MapTo('targetByCycle')
  bool? targetByCycle;

  @MapTo('targetByGroup')
  bool? targetByGroup;

  @MapTo('targetByCompany')
  bool? targetByCompany;

  @MapTo('targetByRegion')
  bool? targetByRegion;

  @MapTo('targetByBudget')
  bool? targetByBudget;

  @MapTo('osaForm')
  bool? osaForm;

  @MapTo('company_id')
  int? companyId;

  @MapTo('item_image')
  String? itemImage;

  @MapTo('targeted')
  int? targeted;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _MeasurementModel {
  @MapTo('measurement_id')
  int? measurementId;

  @MapTo('measurement_type_id')
  int? measurementTypeId;

  @MapTo('measurement_description')
  String? measurementDescription;

  @MapTo('measurement_type_name')
  String? measurementTypeName;

  @MapTo('default_action')
  String? defaultAction;

  @MapTo('measurement_options')
  late List<_MeasurementOptionModel> measurementOptions;

  @MapTo('measurement_conditions_multiple')
  late List<_MeasurementConditionMultipleModel> measurementConditionsMultiple;

  @MapTo('measurement_validations')
  late List<_MeasurementValidationModel> measurementValidations;

  @MapTo('measurement_defaults_result')
  String? measurementDefaultsResult;

  @MapTo('modified_time_stamp_measurement')
  DateTime? modifiedTimeStampMeasurement;

  @MapTo('modified_time_stamp_measurement_defaults_result')
  DateTime? modifiedTimeStampMeasurementDefaultsResult;

  @MapTo('measurement_order_id')
  int? measurementOrderId;

  @MapTo('mandatory_phototypes_count')
  int? mandatoryPhototypesCount;

  @MapTo('optional_phototypes_count')
  int? optionalPhototypesCount;

  @MapTo('measurement_image')
  String? measurementImage;

  @MapTo('companyid')
  int? companyId;

  @MapTo('modified_time_stamp_measurementvalidation')
  DateTime? modifiedTimeStampMeasurementValidation;

  @MapTo('validation_type_id')
  int? validationTypeId;

  @MapTo('required')
  bool? required;

  @MapTo('range_validation')
  String? rangeValidation;

  @MapTo('expression_validation')
  String? expressionValidation;

  @MapTo('error_message')
  String? errorMessage;

  @MapTo('modified_time_stamp_measurementdefault')
  DateTime? modifiedTimeStampMeasurementDefault;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _MeasurementOptionModel {
  @MapTo('measurement_id')
  int? measurementId;

  @MapTo('measurement_option_id')
  int? measurementOptionId;

  @MapTo('measurement_option_description')
  String? measurementOptionDescription;

  @MapTo('modified_time_stamp_measurementoption')
  DateTime? modifiedTimeStampMeasurementOption;

  @MapTo('budget_offset')
  int? budgetOffset;

  @MapTo('budget_offset_type')
  int? budgetOffsetType;

  @MapTo('is_answer')
  bool? isAnswer;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _MeasurementConditionMultipleModel {
  @MapTo('measurement_id')
  int? measurementId;

  @MapTo('measurement_option_id')
  int? measurementOptionId;

  @MapTo('action_measurement_id')
  int? actionMeasurementId;

  @MapTo('action')
  String? action;

  @MapTo('modified_time_stamp_measurementconidtion')
  DateTime? modifiedTimeStampMeasurementCondition;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _MeasurementValidationModel {
  @MapTo('measurement_id')
  int? measurementId;

  @MapTo('validation_type_id')
  int? validationTypeId;

  @MapTo('required')
  bool? required;

  @MapTo('range_validation')
  String? rangeValidation;

  @MapTo('expression_validation')
  String? expressionValidation;

  @MapTo('error_message')
  String? errorMessage;

  @MapTo('modified_time_stamp_measurementvalidation')
  DateTime? modifiedTimeStampMeasurementValidation;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _PhotoTagModel {
  @MapTo('questionpart_id')
  int? questionpartId;

  @MapTo('measurement_id')
  int? measurementId;

  @MapTo('is_mandatory')
  bool? isMandatory;

  @MapTo('PhotoResPerc')
  int? photoResPerc;

  @MapTo('live_images_only')
  bool? liveImagesOnly;

  @MapTo('photo_tag_id')
  int? photoTagId;

  @MapTo('photo_tag')
  String? photoTag;

  @MapTo('number_of_photos')
  int? numberOfPhotos;

  @MapTo('measurement_phototype_id')
  int? measurementPhototypeId;

  @MapTo('image_rec')
  bool? imageRec;

  @MapTo('user_photos')
  late List<_PhotoModel> userPhotos;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _QuestionConditionModel {
  // Structure is unknown from the provided JSON (it's an empty array).
  // This can be populated later if the structure becomes known.
}

@RealmModel(ObjectType.embeddedObject)
abstract class _CommentTypeModel {
  @MapTo('comment_type_id')
  int? commentTypeId;

  @MapTo('comment_type')
  String? commentType;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _QuestionAnswerModel {
  @MapTo('question_id')
  int? questionId;
  String? answer;
  String? comment;
}

// --- Other Models ---

@RealmModel(ObjectType.embeddedObject)
abstract class _PosItemModel {
  @MapTo('item_id')
  int? itemId; // Kept from original, not in JSON

  @MapTo('item_name')
  String? itemName;

  @MapTo('item_amount')
  int? quantity; // Mapped 'quantity' to JSON's 'item_amount'

  @MapTo('photo_url')
  String? photoUrl; // New field
}

@RealmModel(ObjectType.embeddedObject)
abstract class _DocumentModel {
  @MapTo('document_id')
  int? documentId;

  @MapTo('document_name')
  String? documentName;

  // Replaced by 'files' list
  // String? documentUrl;

  @MapTo('project_id')
  int? projectId;

  @MapTo('document_type_id')
  int? documentTypeId;

  @MapTo('document_icon_link')
  String? documentIconLink;

  @MapTo('files')
  late List<_FileModel> files;

  @MapTo('modified_time_stamp_document')
  DateTime? modifiedTimeStampDocument;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _FileModel {
  @MapTo('document_id')
  int? documentId;

  @MapTo('project_id')
  int? projectId;

  @MapTo('document_file_link')
  String? documentFileLink;

  @MapTo('file_id')
  int? fileId;

  @MapTo('modified_time_stamp_file')
  DateTime? modifiedTimeStampFile;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _TaskalertModel {
  @MapTo('message_id')
  int? messageId;

  @MapTo('schedulepeopleid')
  int? schedulepeopleid;

  @MapTo('subject')
  String? subject;

  @MapTo('message')
  String? message;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _TaskmemberModel {
  @MapTo('fullname')
  String? fullname;

  @MapTo('team_lead')
  int? teamLead;

  @MapTo('email')
  String? email;

  @MapTo('schedule_id')
  int? scheduleId;

  @MapTo('task_id')
  int? taskId;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _FollowupTaskModel {
  // Kept original fields
  int? followupTaskId;
  String? description;
  DateTime? dueDate;

  // Added new fields from JSON
  @MapTo('show_followup_icon')
  bool? showFollowupIcon;

  @MapTo('followup_selected')
  bool? followupSelected;

  @MapTo('selected_visit_date')
  DateTime? selectedVisitDate;

  @MapTo('selected_followup_type_id')
  int? selectedFollowupTypeId;

  @MapTo('selected_followup_item_id')
  int? selectedFollowupItemId;

  @MapTo('selected_budget')
  int? selectedBudget;

  @MapTo('selected_followup_type')
  String? selectedFollowupType;

  @MapTo('selected_followup_item')
  String? selectedFollowupItem;

  @MapTo('selected_schedule_note')
  String? selectedScheduleNote;

  @MapTo('followup_number')
  int? followupNumber;
}

@RealmModel(ObjectType.embeddedObject)
abstract class _StocktakeModel {
  @MapTo('task_id')
  int? taskId;

  @MapTo('item_id')
  int? itemId;

  @MapTo('item_code')
  String? itemCode;

  @MapTo('item_name')
  String? itemName;

  @MapTo('item_group')
  String? itemGroup;

  @MapTo('image_url')
  String? imageUrl;

  @MapTo('item_location')
  String? itemLocation;

  @MapTo('item_qty')
  int? itemQty;
}

@RealmModel()
abstract class _CalendarInfoModel {
  @PrimaryKey()
  int id = 0;
  DateTime? timestamp;
  bool? dollarSymbol;
  bool? publicHoliday;
  double? budgetAmount;
}
